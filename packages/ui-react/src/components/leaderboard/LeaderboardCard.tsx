import { cn } from "../../lib/utils"
import { Button } from "../ui/button"
import { ChainIcon } from "../ui/ChainIcon"
import { TokenIcon } from "../ui/TokenIcon"
import type { LeaderboardItem } from "../../types/types"

interface LeaderboardCardProps {
  item: LeaderboardItem
}

export function LeaderboardCard({ item }: LeaderboardCardProps) {
  const formatDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate)
    const end = new Date(endDate)
    const startMonth = start.toLocaleDateString("en-US", { month: "short" }).toUpperCase()
    const endMonth = end.toLocaleDateString("en-US", { month: "short" }).toUpperCase()
    const startDay = start.getDate()
    const endDay = end.getDate()

    return `${startDay} ${startMonth} - ${endDay} ${endMonth}`
  }

  const formatParticipants = (count: number) => {
    if (count >= 1000) {
      return count.toLocaleString()
    }
    return count.toString()
  }

  const renderActionButtons = () => {
    const buttons = []

    switch (item.userAction.type) {
      case "play":
        buttons.push(
          <Button
            key="play"
            className={cn(
              "bg-primary hover:bg-primary/90",
              "text-primary-foreground font-semibold",
              "rounded-full h-10 flex-1",
              "text-[14px]",
            )}
          >
            Play now
          </Button>
        )
        buttons.push(
          <Button
            key="overview"
            variant="secondary"
            className={cn(
              "bg-gray-200 hover:bg-gray-300",
              "text-foreground font-semibold",
              "rounded-full h-10 flex-1",
              "text-[14px]",
            )}
          >
            Overview
          </Button>
        )
        break
      case "claim":
        buttons.push(
          <Button
            key="claim"
            className={cn(
              "bg-primary hover:bg-primary/90",
              "text-primary-foreground font-semibold",
              "rounded-full h-10 flex-1",
              "text-[14px]",
            )}
          >
            Claim {item.userAction.amount} {item.userAction.tokenSymbol}
          </Button>
        )
        buttons.push(
          <Button
            key="overview"
            variant="secondary"
            className={cn(
              "bg-gray-200 hover:bg-gray-300",
              "text-foreground font-semibold",
              "rounded-full h-10 flex-1",
              "text-[14px]",
            )}
          >
            Overview
          </Button>
        )
        break
      case "overview":
        buttons.push(
          <Button
            key="overview"
            variant="secondary"
            className={cn(
              "bg-gray-200 hover:bg-gray-300",
              "text-foreground font-semibold",
              "rounded-full h-10 w-full",
              "text-[14px]",
            )}
          >
            Overview
          </Button>
        )
        break
    }

    return buttons
  }

  return (
    <div
      className={cn(
        "p-4 rounded-[12px]",
        "bg-gray-100",
        "flex flex-col gap-3",
      )}
    >
      {/* Header with rank, date and badge */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-gray-600 text-[14px] font-medium">
            #{item.rank}
          </span>
          <span className="text-gray-500 text-[12px]">
            {formatDateRange(item.startDate, item.endDate)}
          </span>
        </div>
        {item.badgeStatus && (
          <div
            className={cn(
              "px-3 py-1 rounded-full text-[11px] font-medium",
              item.badgeStatus === "pending" && "bg-blue-100 text-blue-600",
              item.badgeStatus === "expired" && "bg-gray-200 text-gray-600",
            )}
          >
            {item.badgeStatus.charAt(0).toUpperCase() + item.badgeStatus.slice(1)}
          </div>
        )}
      </div>

      {/* Title with chain icon */}
      <div className="flex items-center gap-2">
        <ChainIcon chainId={item.chainId} size={20} />
        <h3 className="text-gray-900 font-semibold text-[16px]">{item.title}</h3>
      </div>

      {/* Prize and participants */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-1.5 px-3 py-2 rounded-[8px] bg-green-50 border border-green-200">
          <TokenIcon token={item.prize.token} size={16} />
          <span className="text-gray-900 font-medium text-[14px]">
            {item.prize.token.symbol} {item.prize.amount}
          </span>
        </div>
        <span className="text-gray-500 text-[13px]">
          Participants: {formatParticipants(item.participants)}
        </span>
      </div>

      {/* Action buttons */}
      <div className="flex gap-2">
        {renderActionButtons()}
      </div>
    </div>
  )
}
