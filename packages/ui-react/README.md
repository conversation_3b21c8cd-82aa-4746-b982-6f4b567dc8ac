# BetSwirl UI - React Casino Game Components

This is a **game widget library** for BetSwirl protocol casino games built with React + TypeScript + Vite.

## Available Games

* 🪙 **CoinToss** - Classic heads/tails game with animated coin flip
* 🎲 **Dice** - Roll the dice with customizable win conditions
* 🎰 **Roulette** - European roulette with single zero
* 🎱 **Ken<PERSON>** - Pick your lucky numbers and win big
* 🎡 **Wheel** - Spin the wheel of fortune

## Quick Start

🚀 **Build a Web3 casino with just 20 lines of React code!**

* 📖 [React Integration Guide](https://github.com/BetSwirl/sdk/blob/main/packages/ui-react/docs/react-guide.md) - Step-by-step tutorial
* 🎮 [Live Demo](https://betswirl-ui-react-demo.vercel.app/) - See it in action ([source code](https://github.com/BetSwirl/betswirl-ui-react-demo))
* 🎨 [Storybook](http://demo.betswirl-sdk.chainhackers.xyz/) - Explore all components interactively


## Installation

```shell
npm install @betswirl/ui-react
```

## Features

- 🎯 **5 Casino Games** - Coin<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>
- ⚡ **Fast Integration** - Get started in minutes
- 🎨 **Customizable** - Built with Tailwind CSS
- 🔗 **Web3 Ready** - Works with any wallet provider
- 📱 **Mobile Friendly** - Responsive design
- 🧪 **TypeScript** - Full type safety

## Development

For contributors and maintainers, see [DEVELOPMENT.md](./DEVELOPMENT.md)
